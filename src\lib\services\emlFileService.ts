import { promises as fs } from 'fs'
import path from 'path'
import crypto from 'crypto'
import { prisma } from '@/lib/db'
import { buildMimeEmail } from '@/lib/utils';
import { uploadDir } from '@/config';

export interface EmlFileInfo {
  filePath: string
  fileName: string
  fileSize: number
  fileHash: string
}

export interface AttachmentData {
  name: string
  size: number
  contentType: string
  content: string // base64
  isInline: boolean
  id: string
  encoding: string
}

export interface EmailDetailsForEml {
  from: string
  to: string
  cc: string
  subject: string
  date: string
  body: string
  attachments: AttachmentData[]
}

/**
 * EML文件服务
 * 负责生成、存储和管理EML文件
 */
export class EmlFileService { 
  
  constructor() {
    this.ensureUploadDir()
  }
  
  /**
   * 确保上传目录存在
   */
  private async ensureUploadDir(): Promise<void> {
    try {
      await fs.mkdir(uploadDir, { recursive: true })
    } catch (error) {
      console.error('创建EML上传目录失败:', error)
    }
  }

  /**
   * 将相对路径转换为绝对路径
   */
  private getAbsolutePath(relativePath: string): string {
    return path.join(uploadDir, relativePath)
  }
  
  /**
   * 生成EML文件名
   */
  private generateEmlFileName(subject: string, mailId: string): string {
    // 清理主题，移除特殊字符
    const cleanSubject = subject.replace(/[<>:"/\\|?*]/g, '_').substring(0, 50)
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    const shortMailId = mailId.substring(0, 8)
    return `${cleanSubject}_${shortMailId}_${timestamp}.eml`
  }
  
  /**
   * 从邮件详情生成EML文件
   */
  async generateEmlFromEmailDetails(emailDetails: EmailDetailsForEml, mailId: string): Promise<EmlFileInfo> {
    try {
      // 生成MIME格式的邮件内容
      const mimeContent = buildMimeEmail(emailDetails)
      
      // 生成文件名
      const fileName = this.generateEmlFileName(emailDetails.subject, mailId)
      const filePath = path.join(uploadDir, fileName)
      
      // 写入文件
      await fs.writeFile(filePath, mimeContent, 'utf8')
      
      // 计算文件大小和hash
      const fileBuffer = await fs.readFile(filePath)
      const fileSize = fileBuffer.length
      const fileHash = crypto.createHash('md5').update(fileBuffer).digest('hex')
      
      console.log('EML文件生成成功:', { fileName, fileSize, fileHash })
      
      return {
        filePath: fileName, // 使用相对路径，只保存文件名
        fileName,
        fileSize,
        fileHash
      }
    } catch (error) {
      console.error('生成EML文件失败:', error)
      throw new Error(`生成EML文件失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }
  
  /**
   * 保存附件到TaskAttachment表
   */
  async saveTaskAttachments(taskId: number, attachments: AttachmentData[]): Promise<void> {
    if (!attachments || attachments.length === 0) {
      return
    }
    
    try {
      // 为每个附件创建文件并保存记录
      for (const attachment of attachments) {
        // 生成附件文件名
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
        const fileName = `${taskId}_${timestamp}_${attachment.name}`
        const filePath = path.join(uploadDir, 'attachments', fileName)
        
        // 确保附件目录存在
        await fs.mkdir(path.dirname(filePath), { recursive: true })
        
        // 将base64内容写入文件
        const fileBuffer = Buffer.from(attachment.content, 'base64')
        await fs.writeFile(filePath, fileBuffer)
        
        // 计算文件hash
        const fileHash = crypto.createHash('md5').update(fileBuffer).digest('hex')
        
        // 保存到数据库
        try {
          await prisma.taskAttachment.create({
            data: {
              taskId,
              fileName,
              originalFileName: attachment.name,
              filePath: path.join('attachments', fileName), // 使用相对路径
              fileSize: BigInt(attachment.size),
              fileType: path.extname(attachment.name),
              mimeType: attachment.contentType,
              fileHash,
              isInline: attachment.isInline
            }
          })
          console.log(`附件已保存到数据库: ${fileName}`)
        } catch (dbError) {
          console.error('保存附件到数据库失败，仅保存到文件系统:', dbError)
          console.log(`附件已保存到文件系统: ${fileName}`)
        }
      }
      
      console.log(`成功保存 ${attachments.length} 个附件到任务 ${taskId}`)
    } catch (error) {
      console.error('保存任务附件失败:', error)
      throw new Error(`保存任务附件失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }
  
  /**
   * 删除EML文件
   */
  async deleteEmlFile(filePath: string): Promise<void> {
    try {
      // 如果是相对路径，转换为绝对路径
      const absolutePath = path.isAbsolute(filePath) ? filePath : this.getAbsolutePath(filePath)
      await fs.unlink(absolutePath)
      console.log('EML文件删除成功:', filePath)
    } catch (error) {
      console.error('删除EML文件失败:', error)
      // 不抛出错误，因为文件可能已经不存在
    }
  }
  
  /**
   * 读取EML文件内容
   */
  async readEmlFile(filePath: string): Promise<string> {
    try {
      // 如果是相对路径，转换为绝对路径
      const absolutePath = path.isAbsolute(filePath) ? filePath : this.getAbsolutePath(filePath)
      return await fs.readFile(absolutePath, 'utf8')
    } catch (error) {
      console.error('读取EML文件失败:', error)
      throw new Error(`读取EML文件失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }
  
  /**
   * 获取EML文件信息
   */
  async getEmlFileInfo(filePath: string): Promise<{ size: number; hash: string } | null> {
    try {
      // 如果是相对路径，转换为绝对路径
      const absolutePath = path.isAbsolute(filePath) ? filePath : this.getAbsolutePath(filePath)
      const fileBuffer = await fs.readFile(absolutePath)
      const size = fileBuffer.length
      const hash = crypto.createHash('md5').update(fileBuffer).digest('hex')
      return { size, hash }
    } catch (error) {
      console.error('获取EML文件信息失败:', error)
      return null
    }
  }
}

// 导出单例实例
export const emlFileService = new EmlFileService()
